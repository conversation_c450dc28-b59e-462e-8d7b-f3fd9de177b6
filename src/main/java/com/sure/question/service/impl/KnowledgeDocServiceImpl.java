package com.sure.question.service.impl;

import com.sure.question.document.KnowledgeDoc;
import com.sure.question.service.KnowledgeDocService;
import com.sure.question.service.KnowledgeMainService;
import com.sure.question.util.TreeUtil;
import com.sure.question.vo.KnowledgeMainVo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.function.BiPredicate;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class KnowledgeDocServiceImpl implements KnowledgeDocService {
    private final KnowledgeMainService knowledgeMainService;

    @Override
    public void sync(int gradeLevel, int subjectId) {
        List<KnowledgeDoc> docs = buildDocs(gradeLevel, subjectId);
        deleteDocs(gradeLevel, subjectId);
        insertDocs(docs);
    }

    private List<KnowledgeDoc> buildDocs(int gradeLevel, int subjectId) {
        List<KnowledgeMainVo> knowledgeTree = knowledgeMainService.getGradeLevelSubjectKnowledgeTree(gradeLevel, subjectId);
        List<KnowledgeDoc> docs = new ArrayList<>();
        BiPredicate<KnowledgeMainVo, TreeUtil.TraverseContext<KnowledgeMainVo>> preOrderWithContext = (node, context) -> {
            KnowledgeDoc doc = new KnowledgeDoc();
            doc.setId(node.getId());
            doc.setParent_id(context.parent == null ? 0 : context.parent.getId());
            doc.setGrade_level(gradeLevel);
            doc.setSubject_id(subjectId);
            doc.setName(node.getKnowledgeName());
            String path = context.path.stream().map(KnowledgeMainVo::getKnowledgeName).collect(Collectors.joining("/"));
            doc.setPath(path);
            doc.setDepth(context.depth);
            doc.setChildren_count(context.children.size());
            doc.setIs_leaf(context.isLeaf);
            List<Integer> ancestorIds = context.path.stream().map(KnowledgeMainVo::getId).filter(id -> !Objects.equals(id, node.getId())).collect(Collectors.toList());
            doc.setAncestor_ids(ancestorIds);
            docs.add(doc);
            return true;
        };
        TreeUtil.TraverseOptions<KnowledgeMainVo> traverseOptions = TreeUtil.TraverseOptions.builder()
                        .preOrderWithContext(preOrderWithContext)
                        .childrenGetter(KnowledgeMainVo::getKnowledgeVoList)
                        .build();
        TreeUtil.traverse(knowledgeTree, traverseOptions);
        return docs;
    }

    private void deleteDocs(int gradeLevel, int subjectId) {

    }

    private void insertDocs(List<KnowledgeDoc> docs) {

    }
}
