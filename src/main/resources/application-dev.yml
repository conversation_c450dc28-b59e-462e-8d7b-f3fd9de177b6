server:
  tomcat:
    basedir: /tmp/question-service/upload

spring:
  datasource:
    dynamic:
      primary: tk
      strict: true
      datasource:
        tk:
          url: jdbc:mysql://*************:3306/tk?characterEncoding=utf-8&useSSL=false&serverTimezone=GMT%2B8&allowMultiQueries=true&allowPublicKeyRetrieval=true&rewriteBatchedStatements=true
          username: root
          password: suredev
          driver-class-name: com.mysql.cj.jdbc.Driver
        syslog:
          url: ****************************************************************************************************************************************************************************
          username: root
          password: suredev
          driver-class-name: com.mysql.cj.jdbc.Driver

  redis:
    database: 0
    host: *************
    password: sure@sure
    port: 6379
    timeout: 10000
    jedis:
      pool:
        max-active: 50
        max-wait: 50000
        max-idle: 50
        min-idle: 3

eureka:
  registerWithEureka: true
  fetchRegistry: true
  client:
    service-url:
      defaultZone: http://localhost:8301/eureka
  instance:
    prefer-ip-address: true

aliyun:
  ossTkImg:
    bucketName: tkimgs
    bucketHost: https://tkimgs.igrade.cn/
    endpoint: oss-cn-shenzhen.aliyuncs.com
    accessKeyId: LTAI4GEyZ1NTHtbYQSK21o1D
    accessKeySecret: ******************************
    prefix:
      # 教辅封面
      coachBookCover: coachBook/cover/
      # 首页图片
      frontPageImage: front-page/image/
      # 听力语音
      listening: listening/
      # 上传试卷文件
      uploadPaper: upload-paper/
      # 上传试卷html
      paperHtml: paper-html/
      # 教辅试卷正文、答案pdf
      coachBookPaperContentAnswerPdf: coach-book-paper-content-answer-pdf/
    # 教辅默认封面图片
    coachBookDefaultCover: https://tkimgs.igrade.cn/coachbook/cover/coachBookDefaultCover.png

  ossSureMark:
    bucketName: dev-suremark
    bucketHost: https://dev-suremark.igrade.cn/
    endpoint: oss-cn-shenzhen.aliyuncs.com
    accessKeyId: LTAI4GATD1TSNZeZmgN8aTCJ
    accessKeySecret: ******************************
    prefix:
      # 教辅定制试卷pdf
      customPaperPdf: pdf/customPaper/

  tts:
    appKey: kwsp089JJ6vcAPDG
    accessKeyId: LTAI5t9vLmViTYC5w6fY45ac
    accessKeySecret: ******************************
    url: wss://nls-gateway-cn-shanghai.aliyuncs.com/ws/v1
    tipsAudioUrl: https://dev-suremark.oss-cn-shenzhen.aliyuncs.com/audio/common/dddd.wav

  ocr:
    accessKeyId: LTAI5t9nxjz17FpA3XFDfyt4
    accessKeySecret: ******************************
    endpoint: ocr-api.cn-hangzhou.aliyuncs.com

elasticsearch:
  host: localhost
  port: 9200

xxl-job:
  adminAddresses: http://127.0.0.1:7777/xxl-job-admin
  accessToken: ""
  executor:
    appName: sure-question-jobs
    address: ""
    ip: ""
    port: 9992
    logPath: D:/xxl-job/jobhandler
    logRetentionDays: 30

# 雪花ID配置
snowflake:
  datacenter-id: 22
  worker-id: 22

# 临时文件存放目录
tempPath:
  # 上传试卷
  uploadPaper: "D:/sureTemp/question/upload-paper/"
  # 听力语音合成
  listeningTts: "D:/sureTemp/question/listening-tts/"
  # 教辅反馈卡
  feedbackSheets: "D:/sureTemp/question/feedbackSheets/"

# 允许导入更新知识点章节的用户
allowImportKnowledgeChapterUserIds: "668db2ff-7327-4b13-9e6f-b7dae3f0c134"
